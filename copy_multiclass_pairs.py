"""
Script pour copier les paires images/masques avec masques multi-classes
Prend en entrée un dossier de masques (labelsTr) et un dossier d'images (imagesTr)
Copie seulement les paires où le masque contient plus d'une classe

Auteur: Gabriel Forest
Date: 2025-08-13
"""

import os
import cv2
import numpy as np
import shutil
from pathlib import Path
import time
from datetime import datetime

class MultiClassPairCopier:
    def __init__(self):
        """Initialise le copieur de paires multi-classes"""
        self.processed_count = 0
        self.copied_count = 0
        self.skipped_count = 0
        self.missing_images_count = 0
        self.total_count = 0
        
    def analyze_mask_classes(self, mask_path):
        """
        Analyse les classes présentes dans un masque
        
        Args:
            mask_path (str): Chemin vers le masque
            
        Returns:
            tuple: (unique_values, has_multiple_classes)
        """
        try:
            # Charger le masque en niveaux de gris
            mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
            if mask is None:
                return None, False
                
            # Trouver les valeurs uniques
            unique_values = np.unique(mask)
            
            # Vérifier s'il y a plus d'une classe (plus que juste le background 0)
            non_background_classes = unique_values[unique_values != 0]
            has_multiple_classes = len(non_background_classes) > 0
            
            return unique_values.tolist(), has_multiple_classes
            
        except Exception as e:
            print(f"❌ Erreur lors de l'analyse du masque {mask_path}: {e}")
            return None, False
    
    def find_corresponding_image(self, mask_path, images_folder):
        """
        Trouve l'image correspondante au masque
        
        Args:
            mask_path (Path): Chemin vers le masque
            images_folder (Path): Dossier des images
            
        Returns:
            Path or None: Chemin vers l'image correspondante
        """
        mask_name = mask_path.stem  # nom sans extension
        
        # Extensions d'images supportées
        image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif']
        
        for ext in image_extensions:
            image_path = images_folder / f"{mask_name}{ext}"
            if image_path.exists():
                return image_path
                
        return None
    
    def copy_file_simple(self, src_path, dest_folder):
        """
        Copie un fichier directement dans le dossier de destination
        
        Args:
            src_path (Path): Chemin source
            dest_folder (Path): Dossier de destination
        """
        dest_path = dest_folder / src_path.name
        shutil.copy2(src_path, dest_path)
        return dest_path
    
    def process_folders(self, masks_folder, images_folder, output_masks_folder, output_images_folder):
        """
        Traite les dossiers et copie les paires avec masques multi-classes
        
        Args:
            masks_folder (str): Dossier des masques (labelsTr)
            images_folder (str): Dossier des images (imagesTr)
            output_masks_folder (str): Dossier de sortie pour les masques
            output_images_folder (str): Dossier de sortie pour les images
        """
        self.processed_count = 0
        self.copied_count = 0
        self.skipped_count = 0
        self.missing_images_count = 0
        
        masks_path = Path(masks_folder)
        images_path = Path(images_folder)
        output_masks_path = Path(output_masks_folder)
        output_images_path = Path(output_images_folder)
        
        # Créer les dossiers de sortie
        output_masks_path.mkdir(parents=True, exist_ok=True)
        output_images_path.mkdir(parents=True, exist_ok=True)
        
        # Chercher tous les fichiers de masques PNG
        mask_files = list(masks_path.glob("*.png"))
        self.total_count = len(mask_files)
        
        if self.total_count == 0:
            raise ValueError(f"Aucun fichier PNG trouvé dans le dossier de masques: {masks_folder}")
        
        print(f"📁 {self.total_count} masques trouvés dans {masks_folder}")
        print(f"🔍 Recherche des images correspondantes dans {images_folder}")
        
        # Créer un fichier de log
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = output_masks_path.parent / f"copy_pairs_log_{timestamp}.txt"
        
        with open(log_file, 'w', encoding='utf-8') as log:
            log.write(f"Copie de paires images/masques multi-classes - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            log.write(f"Dossier masques: {masks_folder}\n")
            log.write(f"Dossier images: {images_folder}\n")
            log.write(f"Sortie masques: {output_masks_folder}\n")
            log.write(f"Sortie images: {output_images_folder}\n")
            log.write(f"Nombre total de masques: {self.total_count}\n")
            log.write("=" * 70 + "\n\n")
            
            start_time = time.time()
            
            for i, mask_file in enumerate(mask_files, 1):
                self.processed_count = i
                
                # Analyser les classes du masque
                unique_values, has_multiple_classes = self.analyze_mask_classes(mask_file)
                
                if unique_values is None:
                    self.skipped_count += 1
                    log.write(f"❌ ERREUR - {mask_file.name}: Impossible de lire le masque\n")
                    continue
                
                log.write(f"📋 {mask_file.name}: classes {unique_values}")
                
                if has_multiple_classes:
                    # Chercher l'image correspondante
                    image_file = self.find_corresponding_image(mask_file, images_path)
                    
                    if image_file and image_file.exists():
                        try:
                            # Copier le masque et l'image
                            copied_mask = self.copy_file_simple(mask_file, output_masks_path)
                            copied_image = self.copy_file_simple(image_file, output_images_path)
                            
                            self.copied_count += 1
                            log.write(f" → ✅ COPIÉ\n")
                            log.write(f"   Masque: {copied_mask.name}\n")
                            log.write(f"   Image: {copied_image.name}\n")
                            
                        except Exception as e:
                            self.skipped_count += 1
                            log.write(f" → ❌ ERREUR lors de la copie: {e}\n")
                    else:
                        self.missing_images_count += 1
                        log.write(f" → ⚠️ IGNORÉ: Image correspondante non trouvée ({mask_file.stem})\n")
                else:
                    self.skipped_count += 1
                    log.write(f" → ⏭️ IGNORÉ: Une seule classe (background uniquement)\n")
                
                # Affichage périodique
                if i % 100 == 0 or i == self.total_count:
                    elapsed = time.time() - start_time
                    progress_percent = (i / self.total_count) * 100
                    print(f"📊 [{i:6d}/{self.total_count:6d}] ({progress_percent:5.1f}%) - "
                          f"Copiés: {self.copied_count}, Ignorés: {self.skipped_count}, "
                          f"Images manquantes: {self.missing_images_count}")
            
            # Résumé final dans le log
            total_time = time.time() - start_time
            log.write("\n" + "=" * 70 + "\n")
            log.write("RÉSUMÉ FINAL\n")
            log.write("=" * 70 + "\n")
            log.write(f"Masques traités: {self.processed_count}\n")
            log.write(f"Paires copiées: {self.copied_count}\n")
            log.write(f"Masques ignorés (une classe): {self.skipped_count}\n")
            log.write(f"Images manquantes: {self.missing_images_count}\n")
            log.write(f"Taux de copie: {(self.copied_count/self.processed_count)*100:.1f}%\n")
            log.write(f"Temps total: {total_time:.2f} secondes\n")
        
        print(f"\n✅ Traitement terminé!")
        print(f"📋 Masques traités: {self.processed_count}")
        print(f"✅ Paires copiées: {self.copied_count}")
        print(f"⏭️ Masques ignorés (une classe): {self.skipped_count}")
        print(f"⚠️ Images manquantes: {self.missing_images_count}")
        print(f"📄 Log sauvegardé: {log_file}")

if __name__ == "__main__":
    # === CHEMINS HARDCODÉS ===
    MASKS_FOLDER = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\exemple\labelsTr"
    IMAGES_FOLDER = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\exemple\imagesTr"
    OUTPUT_MASKS_FOLDER = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\exemple_filtered\labelsTr"
    OUTPUT_IMAGES_FOLDER = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\exemple_filtered\imagesTr"
    
    print("🎯 COPIEUR DE PAIRES IMAGES/MASQUES MULTI-CLASSES")
    print("=" * 60)
    print("Ce script copie les paires images/masques où le masque")
    print("contient plus d'une classe (plus que juste le background [0])")
    print("=" * 60)
    print(f"📂 Dossier masques (labelsTr): {MASKS_FOLDER}")
    print(f"🖼️  Dossier images (imagesTr): {IMAGES_FOLDER}")
    print(f"📤 Sortie masques: {OUTPUT_MASKS_FOLDER}")
    print(f"📤 Sortie images: {OUTPUT_IMAGES_FOLDER}")
    print("=" * 60)
    
    # Vérifier que les dossiers d'entrée existent
    if not os.path.exists(MASKS_FOLDER):
        print(f"❌ Dossier de masques introuvable: {MASKS_FOLDER}")
        print("💡 Modifiez la variable MASKS_FOLDER dans le script")
        exit(1)
        
    if not os.path.exists(IMAGES_FOLDER):
        print(f"❌ Dossier d'images introuvable: {IMAGES_FOLDER}")
        print("💡 Modifiez la variable IMAGES_FOLDER dans le script")
        exit(1)
        
    # Confirmation avant traitement
    response = input("Continuer le traitement? (o/N): ")
    if response.lower() not in ['o', 'oui', 'y', 'yes']:
        print("Traitement annulé")
        exit(0)
        
    # Traitement
    copier = MultiClassPairCopier()
    try:
        copier.process_folders(MASKS_FOLDER, IMAGES_FOLDER, OUTPUT_MASKS_FOLDER, OUTPUT_IMAGES_FOLDER)
    except Exception as e:
        print(f"❌ Erreur: {e}")
        exit(1)
